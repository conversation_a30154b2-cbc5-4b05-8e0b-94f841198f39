from flask import Flask, render_template, request, jsonify, Response
import requests
import os
from groq import Groq
import tempfile
import logging
from io import BytesIO
import threading
import time
from collections import deque
import json
import queue
import re
from urllib.parse import urlparse, parse_qs

# Try to import local config, fall back to default config
try:
    from config_local import *
except ImportError:
    from config import *

# Configure logging first
logging.basicConfig(level=getattr(logging, LOG_LEVEL))
logger = logging.getLogger(__name__)

# Try to import youtube_transcript_api after logger is configured
try:
    from youtube_transcript_api import YouTubeTranscriptApi
    YOUTUBE_TRANSCRIPT_AVAILABLE = True
    logger.info("Successfully imported YouTubeTranscript<PERSON>pi at module level")
except ImportError as e:
    YOUTUBE_TRANSCRIPT_AVAILABLE = False
    logger.error(f"Failed to import YouTubeTranscriptApi at module level: {e}")

# Initialize Flask app
app = Flask(__name__)
app.config['SECRET_KEY'] = SECRET_KEY

# Global storage for AI responses - using queue for real-time delivery
ai_response_queues = {}  # Dictionary to store queues for each client
response_lock = threading.Lock()

# Initialize Groq client
groq_client = None

if GROQ_API_KEY:
    try:
        # Try to initialize Groq client
        groq_client = Groq(api_key=GROQ_API_KEY)
        logger.info("Groq client initialized successfully")
    except TypeError as e:
        if "proxies" in str(e):
            logger.error("Groq package compatibility issue detected.")
            logger.error("Please run: pip uninstall groq httpx httpcore -y && pip install groq==0.8.0 httpx==0.27.0")
        else:
            logger.error(f"Failed to initialize Groq client: {e}")
        groq_client = None
    except Exception as e:
        logger.error(f"Failed to initialize Groq client: {e}")
        logger.error("Please check your Groq package version and dependencies")
        groq_client = None

@app.route('/')
def index():
    """Serve the main question page."""
    return render_template('index.html')

@app.route('/api/sources')
def get_sources():
    """Get unique source filenames from ChromaDB for autocomplete."""
    try:
        # Call the existing ChromaDB app to get all documents
        logger.info(f"Fetching documents from: {CHROMADB_BASE_URL}/api/documents")
        response = requests.get(f"{CHROMADB_BASE_URL}/api/documents", timeout=10)

        if response.status_code != 200:
            logger.error(f"Failed to fetch documents from ChromaDB: {response.status_code}")
            logger.error(f"Response content: {response.text}")
            return jsonify({"error": "Failed to fetch sources"}), 500

        data = response.json()
        logger.info(f"ChromaDB response structure: {type(data)}")
        logger.info(f"ChromaDB response keys: {list(data.keys()) if isinstance(data, dict) else 'Not a dict'}")

        # Check if the response has the expected structure
        if data is None:
            logger.error("ChromaDB returned None response")
            return jsonify({"error": "Invalid response from ChromaDB"}), 500

        documents = data.get('documents', [])
        logger.info(f"Retrieved {len(documents)} documents from ChromaDB")

        # Log first document structure for debugging
        if documents and len(documents) > 0:
            logger.info(f"First document structure: {documents[0]}")
            logger.info(f"First document keys: {list(documents[0].keys()) if isinstance(documents[0], dict) else 'Not a dict'}")

        # Extract unique source filenames
        sources = set()
        for doc in documents:
            if doc is None:
                continue

            # The ChromaDB server returns: {"id": "...", "document": "...", "metadata": {...}}
            metadata = doc.get('metadata', {})
            if metadata is None:
                continue

            source = metadata.get('Source')
            if source:
                sources.add(source)
                logger.debug(f"Found source: {source}")

        # Convert to sorted list
        sources_list = sorted(list(sources))

        logger.info(f"Retrieved {len(sources_list)} unique sources")
        return jsonify({"sources": sources_list})

    except requests.RequestException as e:
        logger.error(f"Error connecting to ChromaDB: {e}")
        return jsonify({"error": "Failed to connect to ChromaDB"}), 500
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        return jsonify({"error": "Internal server error"}), 500

@app.route('/api/sources/detailed')
def get_detailed_sources():
    """Get detailed source information for the sources panel."""
    try:
        # Call the existing ChromaDB app to get all documents
        logger.info(f"Fetching documents from: {CHROMADB_BASE_URL}/api/documents")
        response = requests.get(f"{CHROMADB_BASE_URL}/api/documents", timeout=10)

        if response.status_code != 200:
            logger.error(f"Failed to fetch documents from ChromaDB: {response.status_code}")
            return jsonify({"error": "Failed to fetch sources"}), 500

        data = response.json()
        documents = data.get('documents', [])

        # Process documents to create detailed source information
        sources_map = {}
        for doc in documents:
            if doc is None:
                continue

            metadata = doc.get('metadata', {})
            if metadata is None:
                continue

            source = metadata.get('Source')
            if source:
                if source not in sources_map:
                    # Determine source type based on filename/content
                    source_type = 'doc'  # default
                    if source.lower().endswith('.pdf'):
                        source_type = 'pdf'
                    elif 'youtube' in source.lower() or 'youtu.be' in source.lower():
                        source_type = 'youtube'
                    elif source.lower().startswith('pasted') or 'text' in source.lower():
                        source_type = 'text'

                    sources_map[source] = {
                        'id': source,
                        'title': source,
                        'type': source_type,
                        'document_count': 0
                    }

                sources_map[source]['document_count'] += 1

        # Convert to list and sort
        sources_list = list(sources_map.values())
        sources_list.sort(key=lambda x: x['title'])

        logger.info(f"Retrieved {len(sources_list)} detailed sources")
        return jsonify({"sources": sources_list})

    except requests.RequestException as e:
        logger.error(f"Error connecting to ChromaDB: {e}")
        return jsonify({"error": "Failed to connect to ChromaDB"}), 500
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        return jsonify({"error": "Internal server error"}), 500

@app.route('/api/sources/add', methods=['POST'])
def add_source():
    """Add a new source manually."""
    try:
        data = request.get_json()
        if not data or 'title' not in data:
            return jsonify({"error": "No title provided"}), 400

        title = data['title'].strip()
        source_type = data.get('type', 'doc')

        if not title:
            return jsonify({"error": "Title cannot be empty"}), 400

        # For now, we'll just return success since we're not actually storing custom sources
        # In a real implementation, you might want to store these in a database
        new_source = {
            'id': f"custom_{int(time.time())}",
            'title': title,
            'type': source_type,
            'document_count': 0,
            'custom': True
        }

        logger.info(f"Added custom source: {title}")
        return jsonify({"status": "success", "source": new_source})

    except Exception as e:
        logger.error(f"Error adding source: {e}")
        return jsonify({"error": "Internal server error"}), 500

@app.route('/api/test-youtube-import')
def test_youtube_import():
    """Test endpoint to check if youtube-transcript-api is available."""
    return jsonify({
        "youtube_transcript_available": YOUTUBE_TRANSCRIPT_AVAILABLE,
        "import_status": "success" if YOUTUBE_TRANSCRIPT_AVAILABLE else "failed"
    })

@app.route('/api/youtube-transcript', methods=['POST'])
def extract_youtube_transcript():
    """Extract transcript from YouTube video and save to transcript.txt."""
    try:
        logger.info("YouTube transcript extraction endpoint called")

        data = request.get_json()
        logger.info(f"Request data: {data}")

        if not data or 'url' not in data:
            logger.error("No YouTube URL provided in request")
            return jsonify({"error": "No YouTube URL provided"}), 400

        youtube_url = data['url'].strip()
        logger.info(f"YouTube URL received: {youtube_url}")

        if not youtube_url:
            return jsonify({"error": "YouTube URL cannot be empty"}), 400

        # Extract video ID from YouTube URL
        video_id = extract_video_id(youtube_url)
        logger.info(f"Extracted video ID: {video_id}")

        if not video_id:
            return jsonify({"error": "Invalid YouTube URL format"}), 400

        # Check if youtube_transcript_api is available
        if not YOUTUBE_TRANSCRIPT_AVAILABLE:
            logger.error("YouTube transcript API not available")
            return jsonify({"error": "YouTube transcript extraction not available. Please install youtube-transcript-api package."}), 500

        logger.info("YouTube transcript API is available")

        # Get transcript
        logger.info(f"Attempting to extract transcript for video ID: {video_id}")
        try:
            # First, let's see what transcripts are available
            logger.info("Checking available transcripts...")
            transcript_list_obj = YouTubeTranscriptApi.list_transcripts(video_id)

            available_transcripts = []
            for transcript in transcript_list_obj:
                available_transcripts.append({
                    'language': transcript.language,
                    'language_code': transcript.language_code,
                    'is_generated': transcript.is_generated,
                    'is_translatable': transcript.is_translatable
                })

            logger.info(f"Available transcripts: {available_transcripts}")

            # Try to get transcript in multiple ways
            transcript_list = None

            # Method 1: Try to get any available transcript
            try:
                logger.info("Method 1: Trying to get any available transcript")
                for transcript in transcript_list_obj:
                    try:
                        transcript_list = transcript.fetch()
                        logger.info(f"Successfully retrieved transcript using method 1: {transcript.language} ({transcript.language_code})")
                        break
                    except Exception as e:
                        logger.debug(f"Failed to fetch transcript {transcript.language_code}: {e}")
                        continue
            except Exception as e:
                logger.debug(f"Method 1 failed: {e}")

            # Method 2: Try the traditional approach if method 1 failed
            if transcript_list is None:
                logger.info("Method 2: Trying traditional approach")
                transcript_list = YouTubeTranscriptApi.get_transcript(video_id)
                logger.info(f"Successfully retrieved transcript using method 2 with {len(transcript_list)} entries")

        except Exception as transcript_error:
            logger.error(f"Failed to get transcript: {transcript_error}")
            raise transcript_error

        # Combine transcript text
        transcript_text = ""
        for entry in transcript_list:
            transcript_text += entry['text'] + " "

        # Clean up the transcript text
        transcript_text = transcript_text.strip()
        transcript_text = re.sub(r'\s+', ' ', transcript_text)  # Replace multiple spaces with single space

        logger.info(f"Processed transcript text length: {len(transcript_text)} characters")

        if not transcript_text:
            return jsonify({"error": "No transcript text found for this video"}), 400

        # Save to transcript.txt
        transcript_path = os.path.join(os.getcwd(), 'transcript.txt')
        logger.info(f"Saving transcript to: {transcript_path}")

        try:
            with open(transcript_path, 'w', encoding='utf-8') as f:
                f.write(f"YouTube Video: {youtube_url}\n")
                f.write(f"Video ID: {video_id}\n")
                f.write(f"Extracted on: {time.strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write("-" * 50 + "\n\n")
                f.write(transcript_text)
            logger.info(f"Successfully saved transcript to {transcript_path}")
        except Exception as file_error:
            logger.error(f"Failed to save transcript file: {file_error}")
            return jsonify({"error": f"Failed to save transcript file: {str(file_error)}"}), 500

        return jsonify({
            "status": "success",
            "message": "Transcript extracted and saved successfully",
            "video_id": video_id,
            "transcript_length": len(transcript_text),
            "file_path": transcript_path
        })

    except Exception as e:
        logger.error(f"Error extracting YouTube transcript: {e}", exc_info=True)
        error_str = str(e).lower()

        if any(phrase in error_str for phrase in ["no transcript found", "transcript is disabled", "no transcripts", "transcripts disabled"]):
            return jsonify({"error": "No transcript available for this video. The video may not have captions or transcripts may be disabled."}), 400
        elif "video unavailable" in error_str:
            return jsonify({"error": "Video is unavailable or private"}), 400
        elif "no element found" in error_str or "parse error" in error_str:
            return jsonify({"error": "Unable to parse transcript data for this video. The video may not have transcripts available or they may be in an unsupported format."}), 400
        elif "http" in error_str and ("403" in error_str or "404" in error_str):
            return jsonify({"error": "Access denied or video not found. The video may be private, region-restricted, or deleted."}), 400
        else:
            return jsonify({"error": f"Failed to extract transcript: {str(e)}"}), 500

def extract_video_id(youtube_url):
    """Extract video ID from various YouTube URL formats."""
    # Handle different YouTube URL formats
    patterns = [
        r'(?:youtube\.com\/watch\?v=|youtu\.be\/|youtube\.com\/embed\/|youtube\.com\/v\/)([^&\n?#]+)',
        r'youtube\.com\/watch\?.*v=([^&\n?#]+)'
    ]

    for pattern in patterns:
        match = re.search(pattern, youtube_url)
        if match:
            return match.group(1)

    return None

@app.route('/api/speech-to-text', methods=['POST'])
def speech_to_text():
    """Convert uploaded audio to text using Groq Whisper."""
    try:
        # Check if Groq client is available
        if not groq_client:
            return jsonify({"error": "Groq API not configured"}), 500

        if 'audio' not in request.files:
            return jsonify({"error": "No audio file provided"}), 400

        audio_file = request.files['audio']
        if audio_file.filename == '':
            return jsonify({"error": "No audio file selected"}), 400

        # Transcribe using Groq
        logger.info("Transcribing audio with Groq Whisper...")
        transcript_text = transcribe_with_groq(audio_file)

        if transcript_text:
            logger.info(f"Successfully transcribed audio: {transcript_text[:50]}...")
            return jsonify({"text": transcript_text})
        else:
            return jsonify({"error": "Transcription failed"}), 500

    except Exception as e:
        logger.error(f"Speech-to-text error: {e}")
        return jsonify({"error": "Failed to transcribe audio"}), 500

def transcribe_with_groq(audio_file):
    """Transcribe audio using Groq Whisper API."""
    # Read audio file into memory
    audio_data = audio_file.read()
    audio_file.seek(0)  # Reset file pointer

    # Create file tuple for Groq API
    file_tuple = (audio_file.filename or "audio.wav", BytesIO(audio_data), "audio/wav")

    # Call Groq API
    response = groq_client.audio.transcriptions.create(
        model=GROQ_MODEL,
        file=file_tuple,
        language="en",  # Default to English, can be made configurable
        response_format="text"
    )

    return response

@app.route('/api/models')
def get_models():
    """Get available AI models."""
    try:
        models = []
        for model_key, model_config in AI_MODELS.items():
            models.append({
                'id': model_key,
                'name': model_config['name']
            })

        return jsonify({
            "models": models,
            "default_model": DEFAULT_MODEL
        })
    except Exception as e:
        logger.error(f"Error getting models: {e}")
        return jsonify({"error": "Failed to get models"}), 500

@app.route('/api/send-message', methods=['POST'])
def send_message():
    """Send user message to n8n webhook."""
    try:
        data = request.get_json()
        if not data or 'message' not in data:
            return jsonify({"error": "No message provided"}), 400

        message = data['message']
        selected_model = data.get('model', DEFAULT_MODEL)
        # selectedSources removed - all sources are now used by default

        # Validate model selection
        if selected_model not in AI_MODELS:
            logger.error(f"Invalid model selected: {selected_model}")
            return jsonify({"error": f"Invalid model: {selected_model}"}), 400

        # Get webhook URL for selected model
        webhook_url = AI_MODELS[selected_model]['webhook_url']

        if not webhook_url:
            return jsonify({"error": f"Webhook URL not configured for model: {selected_model}"}), 500

        message_id = data.get('messageId', int(time.time()))

        # All sources are now used by default (no filtering)
        logger.info("Query will use all available sources")

        # Use host.docker.internal for Docker containers to reach host machine
        docker_host = "host.docker.internal"

        # Prepare payload for n8n
        payload = {
            "message": message,
            "messageId": message_id,
            "model": selected_model,
            "timestamp": request.headers.get('X-Timestamp', ''),
            "source": "question_page",
            "replyWebhook": f"http://{docker_host}:{PORT}/api/ai-response"
            # selectedSources removed - all sources are used by default
        }

        # Log the payload being sent
        logger.info(f"Sending to {selected_model} model via n8n: {payload}")

        # Send to n8n webhook
        response = requests.post(
            webhook_url,
            json=payload,
            headers={'Content-Type': 'application/json'},
            timeout=30
        )

        if response.status_code == 200:
            logger.info(f"Successfully sent message to {selected_model}: {message[:50]}...")
            logger.info(f"Reply webhook URL: {payload['replyWebhook']}")
            return jsonify({"status": "success", "message": "Message sent successfully", "model": selected_model})
        else:
            logger.error(f"n8n webhook error {response.status_code}: {response.text}")
            return jsonify({"error": f"Failed to send message to {selected_model}"}), 500

    except requests.RequestException as e:
        logger.error(f"Error sending to n8n: {e}")
        return jsonify({"error": "Failed to connect to n8n"}), 500
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        return jsonify({"error": "Internal server error"}), 500

@app.route('/api/ai-response', methods=['POST'])
def receive_ai_response():
    """Receive AI response from n8n webhook."""
    try:
        # Log the raw request for debugging
        logger.info(f"Received AI response webhook call")
        logger.info(f"Request headers: {dict(request.headers)}")
        logger.info(f"Request data: {request.get_data()}")

        data = request.get_json()
        logger.info(f"Parsed JSON data: {data}")
        logger.info(f"Data type: {type(data)}")

        if not data:
            logger.error("No JSON data provided in AI response")
            return jsonify({"error": "No data provided"}), 400

        # Handle the n8n response format: [{"output": "response text"}]
        ai_message = ""
        message_id = ""

        if isinstance(data, list) and len(data) > 0:
            # n8n format: [{"output": "response text"}]
            first_item = data[0]
            logger.info(f"First item in array: {first_item}")
            logger.info(f"First item type: {type(first_item)}")
            logger.info(f"First item keys: {list(first_item.keys()) if isinstance(first_item, dict) else 'Not a dict'}")

            ai_message = first_item.get('output', '')
            message_id = first_item.get('messageId', '')
            logger.info(f"Detected n8n array format")
        elif isinstance(data, dict):
            # Direct format: {"message": "response text", "messageId": "123"}
            logger.info(f"Dict keys: {list(data.keys())}")
            ai_message = data.get('message', '') or data.get('output', '')
            message_id = data.get('messageId', '')
            logger.info(f"Detected direct object format")
        else:
            logger.error(f"Unexpected data format: {type(data)}")

        logger.info(f"Extracted message: '{ai_message}', messageId: '{message_id}'")

        if not ai_message:
            logger.error("No message/output field in AI response data")
            return jsonify({"error": "No message or output provided"}), 400

        # Filter out empty or whitespace-only messages
        if not ai_message.strip():
            logger.info("Received empty message, ignoring")
            return jsonify({"status": "ignored", "message": "Empty message ignored"}), 200

        # Broadcast the response to all connected clients
        response_data = {
            "message": ai_message.strip(),
            "messageId": message_id,
            "timestamp": time.time(),
            "type": "ai_response"
        }

        with response_lock:
            # Send to all connected SSE clients
            for client_id, client_queue in ai_response_queues.items():
                try:
                    client_queue.put_nowait(response_data)
                    logger.info(f"Sent response to client {client_id}")
                except queue.Full:
                    logger.warning(f"Queue full for client {client_id}")

            logger.info(f"Broadcasted AI response: {response_data}")
            logger.info(f"Active SSE clients: {len(ai_response_queues)}")

        logger.info(f"Successfully received AI response for message {message_id}: {ai_message[:50]}...")
        return jsonify({"status": "success", "message": "Response received"})

    except Exception as e:
        logger.error(f"Error receiving AI response: {e}")
        return jsonify({"error": "Internal server error"}), 500

@app.route('/api/stream-responses')
def stream_responses():
    """Server-Sent Events endpoint for real-time AI responses."""
    def event_stream():
        # Create a unique client ID and queue
        client_id = f"client_{int(time.time())}_{threading.current_thread().ident}"
        client_queue = queue.Queue(maxsize=50)

        with response_lock:
            ai_response_queues[client_id] = client_queue
            logger.info(f"New SSE client connected: {client_id}")

        try:
            while True:
                try:
                    # Wait for a response with timeout
                    response_data = client_queue.get(timeout=30)
                    yield f"data: {json.dumps(response_data)}\n\n"
                except queue.Empty:
                    # Send heartbeat to keep connection alive
                    yield f"data: {json.dumps({'type': 'heartbeat'})}\n\n"
                except Exception as e:
                    logger.error(f"Error in SSE stream for {client_id}: {e}")
                    break
        finally:
            # Clean up when client disconnects
            with response_lock:
                if client_id in ai_response_queues:
                    del ai_response_queues[client_id]
                    logger.info(f"SSE client disconnected: {client_id}")

    return Response(event_stream(), mimetype='text/event-stream',
                   headers={'Cache-Control': 'no-cache',
                           'Connection': 'keep-alive'})

@app.route('/api/test-response', methods=['POST'])
def test_ai_response():
    """Test endpoint to simulate an AI response."""
    try:
        data = request.get_json() or {}
        test_message = data.get('message', 'This is a test AI response!')
        message_id = data.get('messageId', 'test-123')

        # Broadcast the test response to all connected clients
        response_data = {
            "message": test_message,
            "messageId": message_id,
            "timestamp": time.time()
        }

        with response_lock:
            # Send to all connected SSE clients
            for client_id, client_queue in ai_response_queues.items():
                try:
                    client_queue.put_nowait(response_data)
                except queue.Full:
                    logger.warning(f"Queue full for client {client_id}")

        logger.info(f"Added test AI response: {test_message}")
        return jsonify({"status": "success", "message": "Test response added"})

    except Exception as e:
        logger.error(f"Error in test response: {e}")
        return jsonify({"error": "Internal server error"}), 500

@app.route('/health')
def health_check():
    """Health check endpoint."""
    docker_host = "host.docker.internal"

    # Check model configurations
    model_status = {}
    for model_key, model_config in AI_MODELS.items():
        model_status[model_key] = {
            "name": model_config['name'],
            "webhook_configured": bool(model_config['webhook_url'])
        }

    return jsonify({
        "status": "ok",
        "message": "Question Page is running",
        "chromadb_configured": bool(CHROMADB_BASE_URL),
        "n8n_configured": bool(N8N_WEBHOOK_URL),
        "groq_configured": bool(GROQ_API_KEY),
        "speech_to_text_available": bool(groq_client),
        "ai_response_webhook": f"http://{docker_host}:{PORT}/api/ai-response",
        "active_sse_clients": len(ai_response_queues),
        "current_host": request.host,
        "docker_webhook_url": f"http://{docker_host}:{PORT}/api/ai-response",
        "available_models": model_status,
        "default_model": DEFAULT_MODEL
    })

if __name__ == '__main__':
    # Check configuration
    print("Starting Question Page Flask app...")
    print(f"ChromaDB URL: {CHROMADB_BASE_URL}")
    print(f"n8n Webhook URL: {N8N_WEBHOOK_URL}")

    # Check speech-to-text configuration
    if groq_client:
        print(f"✓ Groq API configured (Model: {GROQ_MODEL})")
    else:
        print("WARNING: No speech-to-text API configured.")
        print("Please set GROQ_API_KEY in config_local.py or environment variable.")

    if not N8N_WEBHOOK_URL:
        print("WARNING: N8N_WEBHOOK_URL not set. Message sending will not work.")
        print("Please set your n8n webhook URL in config_local.py or environment variable.")

    print(f"Access the app at: http://localhost:{PORT}")

    app.run(debug=DEBUG, host=HOST, port=PORT)