<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>RS Techwin AI Assistant</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
</head>
<body>
    <div class="app-container">
        <!-- Sources Panel -->
        <aside class="sources-panel" id="sources-panel">
            <div class="sources-header">
                <div class="sources-title">Sources</div>
                <button class="panel-toggle-btn" id="panel-toggle" title="Toggle panel">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                        <path d="M15 18l-6-6 6-6" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                    </svg>
                </button>
            </div>

            <div class="sources-actions">
                <button class="action-btn" id="add-source-btn">
                    <span>+</span> Add
                </button>
                <button class="action-btn" id="discover-sources-btn">
                    <svg width="16" height="16" viewBox="0 0 24 24" fill="none">
                        <circle cx="11" cy="11" r="8" stroke="currentColor" stroke-width="2"/>
                        <path d="m21 21-4.35-4.35" stroke="currentColor" stroke-width="2"/>
                    </svg>
                    Discover
                </button>
            </div>

            <div class="select-all-container">
                <label class="select-all-label">
                    <input type="checkbox" id="select-all-sources" class="select-all-checkbox">
                    <span class="checkmark"></span>
                    Select all sources
                </label>
            </div>

            <div class="sources-list" id="sources-list">
                <!-- Sources will be populated here -->
            </div>

            <div class="sources-footer">
                <div class="sources-count" id="sources-count">0 sources selected</div>
            </div>
        </aside>

        <!-- Hidden file input for PDF upload -->
        <input type="file" id="pdf-upload-input" accept="application/pdf" style="display: none;">

        <!-- Floating toggle button (shown when panel is collapsed) -->
        <button class="floating-toggle-btn hidden" id="floating-toggle-btn" title="Show sources">
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                <path d="M9 18l6-6-6-6" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
        </button>

        <!-- Main Content Area -->
        <div class="main-content">
            <!-- Header with branding and model selector -->
            <header class="app-header">
                <div class="header-left">
                    <button class="mobile-sources-toggle" id="mobile-sources-toggle" title="Toggle sources">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none">
                            <path d="M3 12h18M3 6h18M3 18h18" stroke="currentColor" stroke-width="2" stroke-linecap="round"/>
                        </svg>
                    </button>
                    <h1 class="app-title">RS Techwin</h1>
                    <div class="model-selector">
                        <select id="model-select" class="model-dropdown">
                            <option value="ChatGPT">ChatGPT</option>
                            <option value="RSJARVIS">RSJARVIS</option>
                        </select>
                    </div>
                </div>
            </header>

            <!-- Main chat area -->
            <main class="chat-main">
            <div class="chat-container" id="chat-container">
                <!-- Welcome message will be shown when no messages -->
                <div class="welcome-section" id="welcome-section">
                    <div class="welcome-message">
                        <h2>Good to see you.</h2>
                        <p>How can I help you today?</p>
                    </div>
                </div>
                <!-- Chat messages will be added here -->
            </div>
        </main>

            <!-- Input area -->
            <footer class="input-footer">
            <div class="input-container">
                <div class="input-wrapper">
                    <input
                        type="text"
                        id="question-input"
                        placeholder="Ask a question"
                        autocomplete="off"
                        class="message-input"
                    >
                    <div id="autocomplete-dropdown" class="autocomplete-dropdown hidden">
                        <!-- Autocomplete suggestions will be populated here -->
                    </div>
                </div>
                <button id="speech-button" class="mic-button" title="Voice input">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none">
                        <path d="M12 14c1.66 0 3-1.34 3-3V5c0-1.66-1.34-3-3-3S9 3.34 9 5v6c0 1.66 1.34 3 3 3z" fill="currentColor"/>
                        <path d="M17 11c0 2.76-2.24 5-5 5s-5-2.24-5-5H5c0 3.53 2.61 6.43 6 6.92V21h2v-3.08c3.39-.49 6-3.39 6-6.92h-2z" fill="currentColor"/>
                    </svg>
                </button>
            </div>

            <!-- Recording indicator -->
            <div class="recording-indicator hidden" id="recording-indicator">
                <div class="recording-animation">
                    <div class="pulse"></div>
                </div>
                <span>Recording... Release to stop</span>
            </div>

            <!-- Status messages -->
            <div id="status-message" class="status-message hidden">
                <!-- Status messages will appear here -->
            </div>
            </footer>
        </div>
    </div>

    <!-- Loading overlay -->
    <div id="loading-overlay" class="loading-overlay hidden">
        <div class="loading-spinner">
            <div class="spinner"></div>
            <p id="loading-text">Processing...</p>
        </div>
    </div>

    <script src="{{ url_for('static', filename='js/app.js') }}"></script>
</body>
</html>
