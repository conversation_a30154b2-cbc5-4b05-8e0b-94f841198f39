# Local Configuration
# Your actual configuration values

import os

# ChromaDB Configuration (should match your existing setup)
CHROMADB_BASE_URL = "http://localhost:5555"

# n8n Webhook Configuration for different AI models
# ChatGPT Model Webhook URLs
#CHATGPT_WEBHOOK_URL = 'http://localhost:5678/webhook-test/3e04cd5f-9510-4364-b44f-3d2e3f6a572f' # test url
CHATGPT_WEBHOOK_URL = 'http://localhost:5678/webhook/3e04cd5f-9510-4364-b44f-3d2e3f6a572f' # prod url

# RSJARVIS Model Webhook URLs
#RSJARVIS_WEBHOOK_URL = 'http://localhost:5678/webhook-test/a9d20474-dea8-4c13-8b3b-14aa71847780' # test url
RSJARVIS_WEBHOOK_URL = 'http://localhost:5678/webhook/a9d20474-dea8-4c13-8b3b-14aa71847780' # prod url

# Model Configuration
AI_MODELS = {
    'ChatGPT': {
        'name': 'ChatGPT',
        'webhook_url': os.getenv('CHATGPT_WEBHOOK_URL', CHATGPT_WEBHOOK_URL)
    },
    'RSJARVIS': {
        'name': 'RSJARVIS',
        'webhook_url': os.getenv('RSJARVIS_WEBHOOK_URL', RSJARVIS_WEBHOOK_URL)
    }
}

# Default model
DEFAULT_MODEL = 'ChatGPT'

# Legacy support - use ChatGPT as default
N8N_WEBHOOK_URL = AI_MODELS[DEFAULT_MODEL]['webhook_url']

# Groq Configuration
# Groq api key: ********************************************************
# Model: whisper-large-v3
GROQ_API_KEY = os.getenv('GROQ_API_KEY', "********************************************************")
GROQ_MODEL = "whisper-large-v3"

# Flask Configuration
SECRET_KEY = os.getenv('SECRET_KEY', 'your-secret-key-for-production-change-this')
DEBUG = True
HOST = '0.0.0.0'
PORT = 5003  # Changed from 5001 to 5003

# Logging Configuration
LOG_LEVEL = 'INFO'
